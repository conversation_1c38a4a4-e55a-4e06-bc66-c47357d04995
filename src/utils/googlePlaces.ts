// Google Places API utility functions

declare global {
  interface Window {
    google: any;
    initGoogleMaps: () => void;
  }
}

let isGoogleMapsLoaded = false;
let isLoading = false;

export const loadGoogleMapsAPI = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // If already loaded, resolve immediately
    if (isGoogleMapsLoaded && window.google) {
      resolve();
      return;
    }

    // If currently loading, wait for it
    if (isLoading) {
      const checkLoaded = () => {
        if (isGoogleMapsLoaded && window.google) {
          resolve();
        } else {
          setTimeout(checkLoaded, 100);
        }
      };
      checkLoaded();
      return;
    }

    isLoading = true;

    const apiKey = import.meta.env.VITE_GOOGLE_PLACES_API_KEY;
    if (!apiKey) {
      reject(new Error('Google Places API key not found'));
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGoogleMaps`;
    script.async = true;
    script.defer = true;

    // Global callback function
    window.initGoogleMaps = () => {
      isGoogleMapsLoaded = true;
      isLoading = false;
      resolve();
    };

    script.onerror = () => {
      isLoading = false;
      reject(new Error('Failed to load Google Maps API'));
    };

    document.head.appendChild(script);
  });
};

export const initializeAddressAutocomplete = (
  inputElement: HTMLInputElement,
  onAddressSelected?: (addressData: {
    address: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  }) => void
): void => {
  if (!window.google || !window.google.maps) {
    console.error('Google Maps API not loaded');
    return;
  }

  const autocomplete = new window.google.maps.places.Autocomplete(inputElement, {
    types: ['address'],
    componentRestrictions: { country: ['us', 'ca'] }, // Restrict to US and Canada
    fields: ['formatted_address', 'geometry', 'address_components']
  });

  let isPlaceSelected = false;

  autocomplete.addListener('place_changed', () => {
    const place = autocomplete.getPlace();
    console.log('Place changed event fired:', place);

    if (place && place.address_components) {
      isPlaceSelected = true;

      // Parse address components
      let streetNumber = '';
      let route = '';
      let city = '';
      let state = '';
      let country = '';
      let postalCode = '';

      for (const component of place.address_components) {
        const componentType = component.types[0];
        switch (componentType) {
          case "street_number":
            streetNumber = component.long_name;
            break;
          case "route":
            route = component.long_name;
            break;
          case "locality":
            city = component.long_name;
            break;
          case "administrative_area_level_1":
            state = component.short_name;
            break;
          case "country":
            country = component.long_name;
            break;
          case "postal_code":
            postalCode = component.long_name;
            break;
        }
      }

      // Combine street number and route for the full street address
      const fullAddress = `${streetNumber} ${route}`.trim();
      inputElement.value = fullAddress;

      // Call the callback with parsed address data
      if (onAddressSelected) {
        onAddressSelected({
          address: fullAddress,
          city,
          state,
          country,
          postalCode
        });
      }

      // Trigger change event for React
      const event = new Event('input', { bubbles: true });
      inputElement.dispatchEvent(event);

      // Immediately hide and disable the autocomplete
      setTimeout(() => {
        // Force hide any remaining dropdowns
        const pacContainers = document.querySelectorAll('.pac-container');
        pacContainers.forEach(container => {
          (container as HTMLElement).style.display = 'none !important';
          (container as HTMLElement).style.visibility = 'hidden !important';
          (container as HTMLElement).style.opacity = '0 !important';
        });

        inputElement.blur();
        isPlaceSelected = false;
      }, 10);
    }
  });

  // Monitor for pac-container creation and hide it if a place was just selected
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;
          if (element.classList?.contains('pac-container') ||
              element.querySelector?.('.pac-container')) {

            const pacContainer = element.classList?.contains('pac-container')
              ? element
              : element.querySelector('.pac-container') as HTMLElement;

            if (pacContainer && isPlaceSelected) {
              pacContainer.style.display = 'none';
              pacContainer.style.visibility = 'hidden';
            }
          }
        }
      });
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // Handle direct clicks on pac items
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;

    if (target.closest('.pac-item')) {
      isPlaceSelected = true;
      console.log('Pac item clicked directly');

      // Wait for Google to process the click, then hide
      setTimeout(() => {
        const pacContainers = document.querySelectorAll('.pac-container');
        pacContainers.forEach(container => {
          (container as HTMLElement).style.display = 'none';
          (container as HTMLElement).style.visibility = 'hidden';
        });
      }, 50);
    }
  }, true);

  // Hide dropdown when clicking outside
  document.addEventListener('click', (event) => {
    const target = event.target as Node;
    const pacContainer = document.querySelector('.pac-container');

    if (!inputElement.contains(target) &&
        pacContainer &&
        !pacContainer.contains(target)) {
      (pacContainer as HTMLElement).style.display = 'none';
    }
  });
};
