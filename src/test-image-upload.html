<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Test Image Upload Service</h1>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>Click here to select an image</p>
    </div>
    
    <input type="file" id="fileInput" accept="image/*" style="display: none;">
    <button id="uploadBtn" onclick="testUpload()" disabled>Test Upload</button>
    
    <div id="result" class="result" style="display: none;"></div>

    <script>
        let selectedFile = null;
        
        document.getElementById('fileInput').addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                selectedFile = e.target.files[0];
                document.getElementById('uploadBtn').disabled = false;
                document.querySelector('.upload-area').innerHTML = `<p>Selected: ${selectedFile.name}</p>`;
            }
        });

        async function testUpload() {
            if (!selectedFile) return;
            
            const formData = new FormData();
            formData.append('image', selectedFile);
            
            try {
                const response = await fetch('https://image-uploader.mauricio-e1e.workers.dev', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer test-secret-key'
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult(`✅ Upload successful!<br>URL: <a href="${data.url}" target="_blank">${data.url}</a>`, 'success');
                } else {
                    showResult(`❌ Upload failed: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Upload failed: ${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
    </script>
</body>
</html>
