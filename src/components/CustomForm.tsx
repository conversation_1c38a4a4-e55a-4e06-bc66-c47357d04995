import React, { useState, useRef, useEffect } from 'react';
import { Search, Upload, CheckCircle, AlertCircle } from 'lucide-react';
import { loadGoogleMapsAPI, initializeAddressAutocomplete } from '../utils/googlePlaces';
import { uploadImage, getImageUploadConfig, isValidImageType, isValidFileSize } from '../utils/imageUpload';

interface CustomFormProps {
  title?: string;
  webhookUrl?: string;
}

interface FormData {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  projectImage?: File;
  projectImageUrl?: string;
  agreeToTerms: boolean;
}

const CustomForm: React.FC<CustomFormProps> = ({
  title = "Get Your Free Project Estimate",
  webhookUrl = "https://services.leadconnectorhq.com/hooks/XIihUR3iXWQYFe7UPY6Z/webhook-trigger/787fca24-edf0-4a64-b6da-28f4ceeaba69"
}) => {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    state: '',
    country: '',
    postalCode: '',
    agreeToTerms: false
  });

  const [dragActive, setDragActive] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const [uploadError, setUploadError] = useState<string>('');
  const addressInputRef = useRef<HTMLInputElement>(null);

  // Get image upload configuration
  const imageUploadConfig = getImageUploadConfig();

  // Initialize Google Places autocomplete
  useEffect(() => {
    const initializeAutocomplete = async () => {
      try {
        await loadGoogleMapsAPI();
        if (addressInputRef.current) {
          initializeAddressAutocomplete(addressInputRef.current, (addressData) => {
            // Update form data with parsed address components
            setFormData(prev => ({
              ...prev,
              address: addressData.address,
              city: addressData.city,
              state: addressData.state,
              country: addressData.country,
              postalCode: addressData.postalCode
            }));
          });
        }
      } catch (error) {
        console.error('Failed to load Google Places API:', error);
      }
    };

    initializeAutocomplete();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddressFocus = () => {
    // Show the autocomplete dropdown when address field is focused
    const pacContainer = document.querySelector('.pac-container');
    if (pacContainer) {
      (pacContainer as HTMLElement).style.display = 'block';
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const processFileUpload = async (file: File) => {
    // Clear previous errors
    setUploadError('');
    setSubmitStatus('idle');

    // Validate file type
    if (!isValidImageType(file, imageUploadConfig.allowedTypes)) {
      setUploadError('Please select a valid image file (JPEG, PNG, GIF, WebP).');
      return;
    }

    // Validate file size
    if (!isValidFileSize(file, imageUploadConfig.maxFileSize)) {
      const maxSizeMB = (imageUploadConfig.maxFileSize || 10 * 1024 * 1024) / (1024 * 1024);
      setUploadError(`Image file is too large. Please select a file smaller than ${maxSizeMB}MB.`);
      return;
    }

    try {
      setIsUploadingImage(true);

      // Upload image to Cloudflare Workers
      const uploadResult = await uploadImage(file, imageUploadConfig);

      if (uploadResult.success && uploadResult.url) {
        setUploadedFile(file);
        setFormData(prev => ({
          ...prev,
          projectImage: file,
          projectImageUrl: uploadResult.url
        }));
        console.log('Image uploaded successfully:', uploadResult.url);
      } else {
        setUploadError(uploadResult.error || 'Failed to upload image. Please try again.');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      setUploadError('Failed to upload image. Please try again.');
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      await processFileUpload(file);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      await processFileUpload(file);
    }
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      // Prepare form data for HighLevel webhook
      const submissionData: any = {
        // Standard HighLevel fields
        first_name: formData.firstName,
        last_name: formData.lastName,
        phone: formData.phone,
        email: formData.email,
        address: formData.address,
        city: formData.city,
        state: formData.state,
        country: formData.country,
        postal_code: formData.postalCode,

        // Additional fields for compatibility
        firstName: formData.firstName,
        lastName: formData.lastName,
        source: 'Website Form - Horizon Deck Builder',
        timestamp: new Date().toISOString(),
        consent: formData.agreeToTerms ? 'true' : 'false',
        agreeToTerms: formData.agreeToTerms ? 'true' : 'false'
      };

      // Include uploaded image URL if available
      if (formData.projectImageUrl) {
        submissionData.project_image_url = formData.projectImageUrl;
        submissionData.projectImageUrl = formData.projectImageUrl; // Backup field name
        submissionData.imageFileName = formData.projectImage?.name;
        submissionData.imageSize = formData.projectImage?.size;

        // Log image info for debugging
        console.log(`Image uploaded to: ${formData.projectImageUrl}`);
        console.log(`Image size: ${(formData.projectImage?.size || 0 / 1024).toFixed(2)} KB`);
      }

      // Log what we're sending for debugging
      console.log('=== FORM SUBMISSION DEBUG ===');
      console.log('Webhook URL:', webhookUrl);
      console.log('Submission data:', submissionData);

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData)
      });

      // Also send to backup webhook for testing/comparison
      try {
        console.log('Also sending to backup webhook for comparison...');
        await fetch("https://eobhp7b40q43fzm.m.pipedream.net", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...submissionData,
            note: 'Backup webhook - HighLevel primary submission',
            webhook_source: 'horizon_deck_builder',
            location_id: 'XIihUR3iXWQYFe7UPY6Z'
          })
        });
        console.log('Backup webhook sent successfully');
      } catch (backupError) {
        console.log('Backup webhook failed:', backupError);
      }

      // Log response for debugging
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      const responseText = await response.text();
      console.log('Response body:', responseText);

      if (response.ok) {
        console.log('Form submitted successfully!');
        setSubmitStatus('success');
        // Reset form
        setFormData({
          firstName: '',
          lastName: '',
          phone: '',
          email: '',
          address: '',
          city: '',
          state: '',
          country: '',
          postalCode: '',
          agreeToTerms: false
        });
        setUploadedFile(null);
        setUploadError('');
      } else if (response.status === 413) {
        throw new Error('The uploaded image is too large. Please try a smaller image or submit without an image.');
      } else if (response.status === 400) {
        throw new Error('Invalid form data. Please check all fields and try again.');
      } else if (response.status >= 500) {
        throw new Error('Server error. Please try again in a few minutes or call us directly.');
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');

      // Set specific error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('413') || error.message.includes('too large')) {
          setErrorMessage('The uploaded image is too large. Please try a smaller image or submit without an image.');
        } else if (error.message.includes('400') || error.message.includes('Invalid')) {
          setErrorMessage('Please check all form fields and try again.');
        } else if (error.message.includes('500') || error.message.includes('Server')) {
          setErrorMessage('Server error. Please try again in a few minutes or call us directly.');
        } else {
          setErrorMessage(error.message);
        }
      } else {
        setErrorMessage('There was an error submitting your form. Please try again or call us directly.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Success state
  if (submitStatus === 'success') {
    return (
      <div className="w-full max-w-md mx-auto">
        <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
          <div className="px-6 py-4 text-center bg-gradient-to-r from-green-600 to-green-700">
            <h3 className="text-lg sm:text-xl font-bold text-white">Thank You!</h3>
          </div>
          <div className="p-8 text-center">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h4 className="text-xl font-bold text-gray-900 mb-2">Form Submitted Successfully!</h4>
            <p className="text-gray-600 mb-6">
              We've received your request and will contact you within 24 hours to schedule your free consultation.
            </p>
            <button
              onClick={() => setSubmitStatus('idle')}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200"
            >
              Submit Another Request
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
        {/* Form Header */}
        <div className="px-6 py-4 text-center bg-gradient-to-r from-blue-600 to-blue-700">
          <h3 className="text-lg sm:text-xl font-bold text-white">{title}</h3>
        </div>
        
        {/* Form Content */}
        <div className="p-8">
          <form onSubmit={handleSubmit} className="space-y-3">
            {/* Error Message */}
            {submitStatus === 'error' && (
              <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
                <p className="text-sm text-red-700">{errorMessage}</p>
              </div>
            )}

            {/* First Name */}
            <div>
              <input
                type="text"
                name="firstName"
                placeholder="First Name"
                value={formData.firstName}
                onChange={handleInputChange}
                className="w-full h-9 px-4 py-2 border-2 border-gray-300 rounded-md text-sm text-gray-700 placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors duration-200"
                required
                disabled={isSubmitting}
              />
            </div>

            {/* Last Name */}
            <div>
              <input
                type="text"
                name="lastName"
                placeholder="Last Name"
                value={formData.lastName}
                onChange={handleInputChange}
                className="w-full h-9 px-4 py-2 border-2 border-gray-300 rounded-md text-sm text-gray-700 placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors duration-200"
                required
                disabled={isSubmitting}
              />
            </div>

            {/* Phone */}
            <div>
              <input
                type="tel"
                name="phone"
                placeholder="Phone*"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full h-9 px-4 py-2 border-2 border-gray-300 rounded-md text-sm text-gray-700 placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors duration-200"
                required
                disabled={isSubmitting}
              />
            </div>

            {/* Email */}
            <div>
              <input
                type="email"
                name="email"
                placeholder="Email*"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full h-9 px-4 py-2 border-2 border-gray-300 rounded-md text-sm text-gray-700 placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors duration-200"
                required
                disabled={isSubmitting}
              />
            </div>

            {/* Address Search */}
            <div className="relative">
              <input
                ref={addressInputRef}
                type="text"
                name="address"
                placeholder="Search address"
                value={formData.address}
                onChange={handleInputChange}
                onFocus={handleAddressFocus}
                className="w-full h-9 pl-12 pr-4 py-2 border-2 border-gray-300 rounded-md text-sm text-gray-700 placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors duration-200"
                disabled={isSubmitting}
                autoComplete="off"
              />
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <Search className="w-5 h-5 text-gray-400" />
              </div>
            </div>

            {/* Hidden fields for structured address data */}
            <input type="hidden" name="city" value={formData.city} />
            <input type="hidden" name="state" value={formData.state} />
            <input type="hidden" name="country" value={formData.country} />
            <input type="hidden" name="postal_code" value={formData.postalCode} />
            <input type="hidden" name="project_image_url" value={formData.projectImageUrl || ''} />

            {/* File Upload */}
            <div className="mt-4">
              <label
                htmlFor="file-upload"
                className={`relative flex flex-col items-center justify-center w-full h-20 border-2 border-dashed rounded-md cursor-pointer transition-colors duration-200 ${
                  isSubmitting || isUploadingImage
                    ? 'opacity-50 cursor-not-allowed'
                    : dragActive
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={!isSubmitting && !isUploadingImage ? handleDrag : undefined}
                onDragLeave={!isSubmitting && !isUploadingImage ? handleDrag : undefined}
                onDragOver={!isSubmitting && !isUploadingImage ? handleDrag : undefined}
                onDrop={!isSubmitting && !isUploadingImage ? handleDrop : undefined}
              >
                <div className="flex flex-col items-center justify-center pt-2 pb-3">
                  {isUploadingImage ? (
                    <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mb-1"></div>
                  ) : (
                    <Upload className="w-6 h-6 text-gray-400 mb-1" />
                  )}
                  <p className="text-xs text-gray-600 text-center">
                    {isUploadingImage
                      ? 'Uploading image...'
                      : uploadedFile
                        ? uploadedFile.name
                        : 'Image Upload of Project'
                    }
                  </p>
                </div>
                <input
                  id="file-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  disabled={isSubmitting || isUploadingImage}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
                />
              </label>

              {/* Upload Error Message */}
              {uploadError && (
                <div className="flex items-center space-x-2 p-2 mt-2 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-600 flex-shrink-0" />
                  <p className="text-xs text-red-700">{uploadError}</p>
                </div>
              )}
            </div>

            {/* Terms Checkbox */}
            <div className="flex items-start space-x-3 mt-4">
              <input
                type="checkbox"
                name="agreeToTerms"
                checked={formData.agreeToTerms}
                onChange={handleInputChange}
                disabled={isSubmitting}
                className="w-4 h-4 mt-0.5 text-blue-600 bg-blue-50 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 disabled:opacity-50"
                required
              />
              <label className="text-xs text-gray-800 leading-tight">
                I agree to receive text message updates from Deckora. Msg frequency varies (e.g., up to 4/month).
                Msg & data rates may apply. Reply STOP to unsubscribe at any time. View privacy & terms.
              </label>
            </div>

            {/* Submit Button */}
            <div className="mt-6">
              <button
                type="submit"
                disabled={!formData.agreeToTerms || isSubmitting}
                className="w-full h-14 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-bold text-base rounded-md transition-colors duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:transform-none"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Submitting...</span>
                  </div>
                ) : (
                  'Get Started'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CustomForm;